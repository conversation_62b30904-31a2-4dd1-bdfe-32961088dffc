@extends('backend.layout.default')

@push('styling')
<link rel="stylesheet" href="{{ asset('mazer/extensions/sweetalert2/sweetalert2.min.css') }}"/>
    <style>
.picture-container{
    position: relative;
    cursor: pointer;
    text-align: center;
}
.picture{
    width: 120px;
    height: 120px;
    background-color: #999999;
    border: 4px solid #CCCCCC;
    color: #FFFFFF;
    border-radius: 50%;
    margin: 0px auto;
    overflow: hidden;
    transition: all 0.2s;
    -webkit-transition: all 0.2s;
}
.picture:hover{
    border-color: #2ca8ff;
}
.content.ct-wizard-green .picture:hover{
    border-color: #05ae0e;
}
.content.ct-wizard-blue .picture:hover{
    border-color: #3472f7;
}
.content.ct-wizard-orange .picture:hover{
    border-color: #ff9500;
}
.content.ct-wizard-red .picture:hover{
    border-color: #ff3b30;
}
.picture input[type="file"] {
    cursor: pointer;
    display: block;
    height: 50%;
    left: 0;
    opacity: 0 !important;
    position: absolute;
    top: 0;
    /* width: 100%; */
}

.picture-src{
    width: 100%;
    
}
 
#loader {
    display: flex;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100vh;
    justify-content: center;
    align-items: center;
    background: rgba(0,0,0,0.75) no-repeat center center;
    z-index: 99999;
}

.preview-receipt-address p {
   font-size: 11px;
}

#preview-content {
    max-width: 100%;
    overflow: hidden;
    font-size: 0.65rem;
}

#preview-footer {
    overflow-wrap: break-word;
    word-wrap: break-word;
    padding: 0 1rem;
    font-size: 0.65rem;
}

.required-field::after {
    content: " *";
    color: red;
}
    </style>
@endpush

@section('content')

<div class="page-heading">
    <div class="page-title">
      <div class="row">
        <div class="col-12 col-md-6 order-md-1 order-last">
          <h3>Profile Page</h3>
          <p class="text-subtitle text-muted">
            You may update your profile settings here
          </p>
          <div class="col-md-12">
            @include('backend.partials.flash-message')
          </div>
        </div>
        <div class="col-12 col-md-6 order-md-2 order-first">
          <nav
            aria-label="breadcrumb"
            class="breadcrumb-header float-start float-lg-end"
          >
            <ol class="breadcrumb">
              <li class="breadcrumb-item">
                <a href="{{ route('dashboard') }}">Dashboard</a>
              </li>
              <li class="breadcrumb-item active" aria-current="page">
                Profile Page
              </li>
            </ol>
          </nav>
        </div>
      </div>
    </div>
    <section class="section">

     <div class="row match-height">
        <div class="col-md-6 col-12">
          <div class="card">
            <div class="card-header">
                <h4 class="card-title">Basic Info</h4>
            </div>
            <div class="card-content">
              <div class="card-body">
                <form class="form form-horizontal" id="formUpdate">
                    <div class="form-body">
                      <div class="row">
                        <div class="col-md-4">
                          <label>Username</label>
                        </div>
                        <div class="col-md-8">
                          <div class="form-group has-icon-left">
                            <div class="position-relative">
                              @csrf
                              <input type="text" 
                                class="form-control" 
                                placeholder="Name" 
                                id="username"
                                name="username" 
                                value="{{ $user->username }}" 
                                required disabled 
                              />
                              <div class="form-control-icon">
                                <i class="bi bi-person"></i>
                              </div>
                            </div>
                          </div>
                        </div>
                        {{-- <div class="col-md-4">
                          <label>Email</label>
                        </div>
                        <div class="col-md-8">
                          <div class="form-group has-icon-left">
                            <div class="position-relative">
                              <input
                                type="email"
                                class="form-control"
                                placeholder="Email"
                                id="email"
                                name="email"
                                value="{{ $user->email }}"
                                required
                              />
                              <div class="form-control-icon">
                                <i class="bi bi-envelope"></i>
                              </div>
                            </div>
                          </div>
                        </div> --}}
                        <div class="col-md-4">
                          <label>Mobile</label>
                        </div>
                        <div class="col-md-8">
                          <div class="form-group has-icon-left">
                            <div class="position-relative">
                              <input
                                inputmode="numeric"
                                oninput="this.value = this.value.replace(/\D+/g, '')"
                                class="form-control"
                                placeholder="Mobile"
                                name="mobile"
                                id="mobile"
                                value="{{ $detail->mobile }}"
                                required
                              />
                              <div class="form-control-icon">
                                <i class="bi bi-phone"></i>
                              </div>
                            </div>
                          </div>
                        </div>

                        
                        <div class="col-md-4">
                          <label>Sales Person</label>
                        </div>
                        <div class="col-md-8">
                          <div class="form-group has-icon-left">
                            <div class="position-relative">
                              <input
                                type="text"
                                class="form-control"
                                placeholder="Sales Person"
                                id="salesperson"
                                name="salesperson"
                                value="{{ $user->userDetails->first_name }}"
                                required
                              />
                              <div class="form-control-icon">
                                <i class="bi bi-person"></i>
                              </div>
                            </div>
                          </div>
                        </div>                     
                      

                        <div class="col-12 d-flex justify-content-end">
                          <button
                            type="button"
                            class="btn btn-warning me-1 mb-1"
                            data-bs-toggle="modal"
                            data-bs-target="#changePasswordModal"
                          >
                            Change Password
                          </button>
                          <button
                            type="submit"
                            class="btn btn-primary me-1 mb-1"
                            id="updateProfile"
                          >
                            Submit
                          </button>
                          <button
                            type="reset"
                            class="btn btn-light-secondary me-1 mb-1"
                          >
                            Reset
                          </button>
                        </div>
                      </div>
                    </div>
                </form>

                <!-- Change Password Modal -->
                <div class="modal fade" id="changePasswordModal" tabindex="-1" aria-labelledby="changePasswordModalLabel" aria-hidden="true">
                  <div class="modal-dialog">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title" id="changePasswordModalLabel">Change Password</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                      </div>
                      <form id="changePasswordForm">
                        <div class="modal-body">
                          @csrf
                          @if (Str::contains($user->access_module, 'plats'))
                            <div class="form-group">
                              <label for="plats_email">Registered Email</label>
                              <input type="email" class="form-control" id="plats_email" name="plats_email">
                            </div>
                          @endif
                          <div class="form-group">
                            <label for="current_password">Current Password</label>
                            <div class="input-group">
                              <input type="password" class="form-control" id="current_password" name="current_password" required>
                              <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('current_password', this)">
                                <i class="bi bi-eye-slash"></i>
                              </button>
                            </div>
                          </div>
                          <div class="form-group">
                            <label for="new_password">New Password</label>
                            <div class="input-group">
                              <input type="password" class="form-control" id="new_password" name="new_password" required>
                              <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('new_password', this)">
                                <i class="bi bi-eye-slash"></i>
                              </button>
                            </div>
                          </div>
                          <div class="form-group">
                            <label for="new_password_confirmation">Confirm New Password</label>
                            <div class="input-group">
                              <input type="password" class="form-control" id="new_password_confirmation" name="new_password_confirmation" required>
                              <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordVisibility('new_password_confirmation', this)">
                                <i class="bi bi-eye-slash"></i>
                              </button>
                            </div>
                          </div>
                          <script>
                            function togglePasswordVisibility(inputId, button) {
                              const input = document.getElementById(inputId);
                              const icon = button.querySelector('i');
                              if (input.type === 'password') {
                                input.type = 'text';
                                icon.classList.replace('bi-eye-slash', 'bi-eye');
                              } else {
                                input.type = 'password';
                                icon.classList.replace('bi-eye', 'bi-eye-slash');
                              }
                            }
                          </script>
                        </div>
                        <div class="modal-footer">
                          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                          <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

          <div class="col-md-6 col-12">
            <div class="card">
              <div class="card-header">
                <h4 class="card-title">Profile Picture</h4>
              </div>
            
              <div class="card-content">
                <div class="card-body">
                    <div class="picture-container">
                        <form enctype="multipart/form-data">
                        <div class="picture">
                          @if ($detail->avatar != null && $user->isBizappUser == 'N')
                            <img 
                            src="{{ url('uploads/images/useravatar/' . $detail->avatar) }}?v={{ time() }}" 
                            class="picture-src" 
                            id="wizardPicturePreview" 
                            title="image"
                          >
                          @elseif ($detail->avatar != null && $user->isBizappUser == 'Y')
                            <img 
                            src="{{ ('https://corrad.visionice.net/bizapp/upload/profile/' . $detail->avatar) }}" 
                            class="picture-src"
                            id="wizardPicturePreview" 
                            title="image"
                          >
                          @else
                            <img src="{{  asset('mazer/images/faces/1.jpg') }}" class="picture-src" id="wizardPicturePreview" title="">
                          @endif
                            <input type="file" id="wizard-picture" class="" name="avatar">
                        </div>
                         <h6 class="">Choose Picture</h6>
                         <button
                            type="submit"
                            class="btn btn-primary me-1 mb-1"
                            id="updateAvatar">
                            Update
                          </button>
                        </form>
                    </div>
                </div>
              </div>
              <div class="card-header">
              </div>
            </div>
          </div>

          <!-- // Basic multiple Column Form section start -->
          @if (auth()->user()->companies)
          <section id="multiple-column-form">
            <div class="row match-height">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Business Info</h4>
                        </div>
                        <div class="card-content">
                            <div class="card-body">
                                <form class="form" id="business_info_form">
                                  @csrf

                                    <div class="row">
                                      <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="comname-column">Company Name</label>
                                            <input type="text" id="comname-column" class="form-control"
                                                value="{{ $user->companies->com_name }}"
                                                placeholder="Company Name" name="comname">
                                        </div>
                                        </div>

                                      <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="ssm-column">Company Registration Number (SSM)</label>
                                            <input type="text" id="ssm-column" class="form-control"
                                                value="{{ $user->companies->com_registration_no }}"
                                                placeholder="SSM" name="ssm">
                                        </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="address-column">Address</label>
                                                <input type="text" id="address-column" class="form-control"
                                                    value="{{ $user->companies->com_address }}"
                                                    placeholder="Address" name="address">
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="postcode-column">Postcode</label>
                                                <input type="text" id="postcode-column" class="form-control"
                                                    value="{{ $user->companies->com_postcode }}"
                                                    placeholder="Postcode" name="postcode">
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                            <div class="form-group">
                                                <label for="city-column">City</label>
                                                <input type="text" id="city-column" class="form-control" placeholder="City"
                                                value="{{ $user->companies->com_city }}" name="city">
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-12">
                                          <div class="form-group">
                                              <label for="country-floating">Country</label>
                                              <select id="country-floating" class="form-select" name="country" {{ $user->companies->com_country ? 'disabled' : '' }}>
                                                @php
                                                    $countries = ['MALAYSIA'];
                                                @endphp
                                                @foreach($countries as $country)
                                                    <option value="{{ $country }}" {{ $user->companies->com_country == $country ? 'selected' : '' }}>
                                                        {{ $country }}
                                                    </option>
                                                @endforeach
                                            </select>
                                            
                                            @if($user->companies->com_country)
                                                <input type="hidden" name="country" value="{{ $user->companies->com_country }}">
                                            @endif
                                          </div>
                                      </div>
                                        <div class="col-md-6 col-12">
                                          <div class="form-group">
                                              <label for="state-column">State</label>
                                    
                                                  <!-- Show dropdown if the country is MALAYSIA -->
                                                  <select id="state-column" class="form-select" name="state" >
                                                      @php
                                                          $states = [
                                                            '1' => 'Johor',
                                                            '2' => 'Kedah',
                                                            '3' => 'Kelantan',
                                                            '4' => 'Melaka',
                                                            '5' => 'Negeri Sembilan',
                                                            '6' => 'Pahang',
                                                            '7' => 'Penang',
                                                            '8' => 'Perak',
                                                            '9' => 'Perlis',
                                                            '10' => 'Selangor',
                                                            '11' => 'Terengganu',
                                                            '12' => 'Sabah',
                                                            '13' => 'Sarawak',
                                                            '14' => 'Wilayah Persekutuan Kuala Lumpur',
                                                            '15' => 'Wilayah Persekutuan Labuan',
                                                            '16' => 'Wilayah Persekutuan Putrajaya'
                                                          ];
                                                      @endphp
                                                      @foreach($states as $key => $state)
                                                          <option value="{{ $key }}" {{ $user->companies->com_state == $key ? 'selected' : '' }}>
                                                              {{ $state }}
                                                          </option>
                                                        
                                                      @endforeach
                                                  </select>
                                      
                                          </div>
                                      </div>
                                      
                                      <hr class="mt-3">

                                    <div class="col-md-6 col-12">
                                        <div class="form-group">
                                            <label for="sst-num-column">SST Number</label>
                                            <input type="text" id="sst-num-column" class="form-control" placeholder="SST Number"
                                            value="{{ $user->companies->com_sst_number }}" name="sst_num">
                                        </div>
                                    </div>
                                    <div class="row">

                                      <div class="col-md-3 col-12">
                                        <div class="form-group">
                                            <label for="sst-percent-column">SST Percentage (%)</label>
                                            <input type="number" id="sst-percent-column" class="form-control" placeholder="SST Percentage (%)"
                                                value="{{ $user->companies->com_sst_value }}" name="sst_percent" min="1" max="100" step="1">
                                        </div>
                                      </div>
                                      <div class="col-md-3 col-12 d-flex align-items-center">
                                        <div class="form-check">
                                          <div class="custom-control custom-checkbox">
                                              <input type="checkbox"
                                                  class="form-check-input form-check-primary form-check-glow"
                                                  name="customCheck" id="checkboxGlow1">
                                              <label class="form-check-label" for="checkboxGlow1">Not charging SST</label>
                                          </div>
                                      </div>
                                      </div>
                                    </div>
                                      
                                        <div class="col-12 d-flex justify-content-end">
                                            <button type="submit" class="btn btn-primary me-1 mb-1">Submit</button>
                                            <button type="reset" class="btn btn-light-secondary me-1 mb-1" id="reset-button">Reset</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
          </section>

          @php
           $sstNo = null;
           $firstPart = null;

          if ($receipt !== null) {
              $sstNo = $receipt->sst ?? null;
              if ($receipt->sst !== null) {
                  $parts = explode(',', $receipt->sst);
                  $firstPart = $parts[0] ?? null;
              }
          }

          @endphp
          <!-- Add Receipt Display Section below Business Info -->            
          <section id="receipt-display-section" class="mt-4">
            <div class="row match-height">
              <!-- Receipt Display Form Column -->
              <div class="col-md-6 col-12">
                <div class="card">
                  <div class="card-header">
                    <h4 class="card-title">Receipt Display Form</h4>
                  </div>
                  <div class="card-content">
                    <div class="card-body">
                      <form class="form" id="receipt_display_form" method="POST" action="{{ route('profile.receipt.update') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="row">
                          <!-- Logo Upload Section at the top -->
                          <div class="col-md-12 mb-4">
                            <div class="d-flex align-items-center my-2">
                              <hr class="flex-grow-1">
                              <span class="mx-3">Company Logo</span>
                              <hr class="flex-grow-1">
                            </div>
                            <div class="row">
                              <div class="col-md-6">
                                <label>Company Logo</label>
                                <div class="form-group">
                                  <input type="file" 
                                    class="form-control" 
                                    id="receipt_logo" 
                                    name="receipt_logo"
                                    accept="image/*"
                                    onchange="previewLogo(this)"
                                  />
                                  <small class="text-muted">Recommended: Square logo (1:1 ratio), Max 2MB, PNG/JPG format</small>
                                </div>
                              </div>
                              <div class="col-md-6">
                                <label>Logo Preview</label>
                                <div class="form-group">
                                  <div class="logo-preview-container" style="border: 2px dashed #ddd; padding: 20px; text-align: center; min-height: 100px; display: flex; align-items: center; justify-content: center;">
                                    @if($receipt && $receipt->logo)
                                      <img id="logo-preview" src="{{ Storage::disk('s3')->url($receipt->logo) }}" alt="Logo Preview" style="max-width: 100px; max-height: 100px; object-fit: contain;">
                                    @else
                                      <img id="logo-preview" src="#" alt="Logo Preview" style="max-width: 100px; max-height: 100px; object-fit: contain; display: none;">
                                      <span id="logo-placeholder" class="text-muted">No logo uploaded</span>
                                    @endif
                                  </div>
                                  @if($receipt && $receipt->logo)
                                    <button type="button" class="btn btn-sm btn-danger mt-2" onclick="removeLogo()">
                                      <i class="bi bi-trash"></i> Remove Logo
                                    </button>
                                  @endif
                                </div>
                              </div>
                            </div>
                            <div class="d-flex align-items-center my-3">
                              <hr class="flex-grow-1">
                              <span class="mx-3">Company Information</span>
                              <hr class="flex-grow-1">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="required-field">Company Name</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text"
                                  class="form-control"
                                  placeholder="Company Name"
                                  id="receipt_title"
                                  name="receipt_title"
                                  value="{{ $receipt->title ?? '' }}"
                                  required
                                  data-emoji-validation="true"
                                />
                                <small class="text-muted">⚠️ Emojis not allowed - may cause printer issues</small>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="required-field">SSM No.</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Receipt SSM" 
                                  id="receipt_ssm" 
                                  name="receipt_ssm"
                                  value="{{ $receipt->ssm ?? '' }}" 
                                  required 
                                />
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label>SST No.</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Receipt SST" 
                                  id="receipt_sst" 
                                  name="receipt_sst"
                                  value="{{ $firstPart }}" 
                                />
                              </div>
                            </div>
                          </div>
                          <div class="d-flex align-items-center my-2">
                            <hr class="flex-grow-1">
                            <span class="mx-3">Address</span>
                            <hr class="flex-grow-1">
                          </div>
                          <div class="col-md-12">
                            <label class="required-field">Receipt Address</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text"
                                  class="form-control"
                                  placeholder="Receipt Address"
                                  id="receipt_address"
                                  name="receipt_address"
                                  value="{{ $receipt->address ?? '' }}"
                                  required
                                  data-emoji-validation="true"
                                />
                                <small class="text-muted">⚠️ Emojis not allowed - may cause printer issues</small>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="required-field">Postcode</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Postcode" 
                                  id="receipt_postcode" 
                                  name="receipt_postcode"
                                  value="{{ $receipt->postcode ?? '' }}" 
                                  required 
                                />
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label class="required-field">City</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="City" 
                                  id="receipt_city" 
                                  name="receipt_city"
                                  value="{{ $receipt->city ?? '' }}" 
                                  required 
                                />
                              
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label>Country</label>
                            <div class="form-group">
                              <div class="position-relative">
                                @php
                                  $countries = ['MALAYSIA'];
                                @endphp
                                <select id="receipt_country" name="receipt_country" class="form-select">
                                  @foreach($countries as $country)
                                    <option value="{{ $country }}" {{ (isset($receipt) && $receipt->country == $country) ? 'selected' : '' }}>
                                      {{ $country }}
                                    </option>
                                  @endforeach
                                </select>
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label>State</label>
                            <div class="form-group">
                              <div class="position-relative">
                                @php
                                  $states = [
                                    '1' => 'Johor',
                                    '2' => 'Kedah',
                                    '3' => 'Kelantan',
                                    '4' => 'Melaka',
                                    '5' => 'Negeri Sembilan',
                                    '6' => 'Pahang',
                                    '7' => 'Penang',
                                    '8' => 'Perak',
                                    '9' => 'Perlis',
                                    '10' => 'Selangor',
                                    '11' => 'Terengganu',
                                    '12' => 'Sabah',
                                    '13' => 'Sarawak',
                                    '14' => 'Wilayah Persekutuan Kuala Lumpur',
                                    '15' => 'Wilayah Persekutuan Labuan',
                                    '16' => 'Wilayah Persekutuan Putrajaya'
                                  ];
                                @endphp
                                <select id="receipt_state" name="receipt_state" class="form-select">
                                  @foreach($states as $key => $state)
                                    <option value="{{ $key }}" {{ (isset($receipt) && $receipt->state == $key) ? 'selected' : '' }}>
                                      {{ $state }}
                                    </option>
                                  @endforeach
                                </select>
                              </div>
                            </div>
                          </div>
                          <div class="d-flex align-items-center my-2">
                            <hr class="flex-grow-1">
                            <span class="mx-3">Misc.</span>
                            <hr class="flex-grow-1">
                          </div>
                          <div class="col-md-6">
                            <label>Email</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Email" 
                                  id="receipt_email" 
                                  name="receipt_email"
                                  value="{{ $receipt->email ?? '' }}" 
                                />
                              </div>
                            </div>
                          </div>
                          <div class="col-md-6">
                            <label>Contact No.</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Contact No." 
                                  id="receipt_phone" 
                                  name="receipt_phone"
                                  value="{{ $receipt->phone ?? '' }}" 
                                />
                              </div>
                            </div>
                          </div>
                         
                          <div class="col-md-6">
                            <label>Salesperson</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <input type="text" 
                                  class="form-control" 
                                  placeholder="Sales Person Name" 
                                  id="receipt_salesperson" 
                                  name="receipt_salesperson"
                                  value="{{ $receipt->name ?? '' }}" 
                                />
                              </div>
                            </div>
                          </div>
                          <div class="col-md-12">
                            <label>Footer Text</label>
                            <div class="form-group">
                              <div class="position-relative">
                                <textarea
                                  class="form-control"
                                  placeholder="Footer Text"
                                  id="receipt_footer"
                                  name="receipt_footer"
                                  maxlength="500"
                                  rows="3"
                                  data-emoji-validation="true"
                                >{{ $receipt->tnc ?? '' }}</textarea>
                                <div class="d-flex justify-content-between">
                                  <small class="text-muted">⚠️ Emojis not allowed - may cause printer issues</small>
                                  <small class="text-muted"><span id="charCount">0</span>/500 characters</small>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="col-12 d-flex justify-content-end">
                          <button type="submit" class="btn btn-primary me-1 mb-1">Submit</button>
                          <button type="reset" class="btn btn-light-secondary me-1 mb-1" id="reset-receipt-button">Reset</button>
                        </div>
                      </form>
                    </div>
                  </div>
                </div>
              </div>
         
              <div class="col-md-6 col-12">
              <div class="bg-white p-3" id="receipt-preview-container">
                <div class="card-header">
                  <h5 class="card-title">Preview</h5>
                </div>
                <p>This is a preview only. Actual printed receipts may vary slightly.</p>

                <div class="col-md-6" style="justify-self: center;">
                <div id="preview-content" class="min-h-[500px] flex flex-col border border-gray-300 rounded-lg shadow-sm">
                    <div class="text-center mb-8 preview-receipt-address">
                        <!-- Logo Preview -->
                        @if($receipt && $receipt->logo)
                            <div class="mb-3 pt-4">
                                <img id="preview-logo" src="{{ Storage::disk('s3')->url($receipt->logo) }}" alt="Company Logo" style="max-width: 80px; max-height: 80px; object-fit: contain;">
                            </div>
                        @else
                            <div id="preview-logo-container" class="mb-3" style="display: none;">
                                <img id="preview-logo" src="#" alt="Company Logo" style="max-width: 80px; max-height: 80px; object-fit: contain;">
                            </div>
                        @endif
                        <h5 class="font-bold text-lg mb-2 pt-3" id="preview-title">{{ $receipt->title ?? 'Company Name' }}</h5>
                        <p class="mb-1 " id="preview-ssm">( {{ $receipt->ssm ?? 'SSM' }} )</p>
                        <p class="mb-1 " id="preview-sst">SST NO : {{ $receipt->sst ?? 'SSM' }} </p>
                        <p class="mb-1 " id="preview-address">{{ $receipt->address ?? 'Company Address' }}</p>
                        <p class="mb-1" id="preview-city-postcode">
                            <span id="preview-city">{{ $receipt->city ?? 'City' }}</span> - 
                            <span id="preview-postcode">{{ $receipt->postcode ?? 'Postcode' }}</span>
                        </p>
                        <p class="mb-1" id="preview-country-state">
                            <span id="preview-state">{{ isset($receipt->state) ? $states[$receipt->state] ?? 'State' : 'State' }}</span>
                            <span id="preview-country">{{ $receipt->country ?? 'Country' }}</span>
                        </p>
                        <p class="mb-1" id="preview-phone">
                          <span id="preview-phone">TEL : {{ $receipt->phone ?? '601' }}</span>
                        </p>
                        <p class="mb-1" id="preview-email">
                          <span id="preview-email">EMAIL : {{ $receipt->email ?? 'email' }}</span>
                        </p>
                        <p class="mb-1" id="preview-salesman">
                          <span id="preview-salesman">SALES PERSON : {{ $receipt->name ?? 'email' }}</span>
                        </p>
                    </div>
                    
                    <div class="border-t border-b border-gray-300 py-4 mb-8">
                        <div class="text-center">
                            <h5 class="font-bold text-lg mb-2">RECEIPT</h5>
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-300 pt-4 mt-8">
                        <p class="text-center text-sm" id="preview-footer">{{ $receipt->tnc ?? 'Footer Text' }}</p>
                    </div>
                </div>
            </div>
          </div>
          </div>
            </div>
          </section>
          @endif
          
      </div>

    </section>
  </div>

  @stop

@push('scripts')
<!-- Emoji Validation Script -->
<script src="{{ asset('js/emoji-validation.js') }}"></script>
<script>

    $(document).ready(function(){
        // Handle password change form submission
        $('#changePasswordForm').on('submit', function(e) {
            e.preventDefault();
            const formData = $(this).serializeArray();
            const url = "{{ route('profile.password.update') }}";
            
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                beforeSend: function() {
                    $('#changePasswordForm button[type="submit"]').prop('disabled', true).html('Changing...');
                },
                success: function(response) {
                    $('#changePasswordForm button[type="submit"]').prop('disabled', false).html('Change Password');
                    if(response.result) {
                        alert_success(response.message);
                        $('#changePasswordModal').modal('hide');
                        $('#changePasswordForm')[0].reset();
                        if (response.redirect) {
                            setTimeout(() => {
                                window.location.href = response.redirect;
                            }, 4500);
                        }
                    } else {
                        alert_error(response.message);
                    }
                },
                error: function(xhr) {
                    $('#changePasswordForm button[type="submit"]').prop('disabled', false).html('Change Password');
                    alert_error(xhr.responseJSON?.message || 'Something went wrong');
                }
            });
        });
    $('#formUpdate').on('submit', function(){
      const data = $(this).serializeArray();
      const url = "{{ route('profile.update') }}";
      curl_post(url, data, false);

      return false;
    });

    $('#business_info_form').on('submit', function(){
      const data = $(this).serializeArray();
      const url = "{{ route('profile.business.update') }}";
      curl_post(url, data, false);

      return false;
    });

    $('#updateAvatar').on('click', function(){
      const formData = new FormData();
      formData.append('avatar', $('#wizard-picture')[0].files[0]);
      formData.append('_token', "{{ csrf_token() }}");
      const url = "{{ route('profile.pic.update') }}";
      
      $.ajax({
        url: url,
        type: 'POST',
        data: formData,
        contentType: false,
        processData: false,
        beforeSend: function(){
          disabled_element();
          $('#updateAvatar').html('Updating...');
        },
        success: function(response){
          enable_element();
          $('#updateAvatar').html('Update');
          if(response.result){
            alert_success(response.message);
          } else {
            alert_error(response.message);
          }
        },
        error: function(err){
          enable_element();
          $('#updateAvatar').html('Update');
          alert_error('Something went wrong');
        }
      });

      return false;
    });

    $("#wizard-picture").change(function(){
      if(this.files && this.files[0]) {
        var reader = new FileReader();
        reader.onload = function (e) {
            $('#wizardPicturePreview').attr('src', e.target.result).fadeIn('slow');
        }
        reader.readAsDataURL(this.files[0]);
      }
    });

// Define toggleStateField to avoid "undefined" errors
function toggleStateField() {
    var countryVal = $("#country-floating").val();
    // Enable state selector if country is MALAYSIA; otherwise, disable it.
    if (countryVal === "MALAYSIA") {
        $("#state-column").prop("disabled", false);
    } else {
        $("#state-column").prop("disabled", true);
    }
}

// When document is ready
$(document).ready(function() {
    // Initial toggle on page load
    toggleStateField();
    
    // Add event listener (alternative to inline onchange)
    $("#country-floating").on('change', function() {
        toggleStateField();
    });

});

 // Reset business form on reset button click
 $('#reset-button').on('click', function() {
        document.getElementById("business_info_form").reset();
        toggleStateField(); // Ensure the state select is toggled based on the new country value
    });

  });
</script>
   
    <script>

      $(document).ready(function() {
          $('#sst-percent-column').on('input', function() {
              var value = $(this).val();
              var regex = /^[1-9]\d*$/; // Matches positive integers without leading zeros

              if (!regex.test(value)) {
                  $(this).val('');
                  alert('The SST percentage cannot have leading zeros.');
              }
          });
      });

      $(document).ready(function() {
          $('#checkboxGlow1').on('change', function() {
              if ($(this).is(':checked')) {
                  $('#sst-percent-column').val(null);
                  $('#sst-percent-column').prop('disabled', true); // Optional: Disable the input field
              } else {
                  $('#sst-percent-column').prop('disabled', false); // Optional: Enable the input field
              }
          });
      });
    </script>

    <script>
    // Function to update preview
    function updatePreview() {
         const title = document.getElementById('receipt_title')?.value || 'Company Name';
         const address = document.getElementById('receipt_address')?.value || 'Company Address';
         const postcode = document.getElementById('receipt_postcode')?.value || 'Postcode';
         const city = document.getElementById('receipt_city')?.value || 'City';
         const country = document.getElementById('receipt_country')?.value || 'Country';
         const stateSelect = document.getElementById('receipt_state');
         const stateText = stateSelect ? stateSelect.options[stateSelect.selectedIndex].text : 'State';
         const footer = document.getElementById('receipt_footer')?.value || 'Footer Text';
         const phone = document.getElementById('receipt_phone')?.value || 'Contact No';
         const email = document.getElementById('receipt_email')?.value || 'Email';
         const ssm = document.getElementById('receipt_ssm')?.value || 'SSM No';
         const sst = document.getElementById('receipt_sst')?.value || 'SST';
         const salesPerson = document.getElementById('receipt_salesperson')?.value || 'Sales Person';

         // Update preview elements
         document.getElementById('preview-title').textContent = title;
         document.getElementById('preview-address').textContent = address;
         document.getElementById('preview-city').textContent = city;
         document.getElementById('preview-postcode').textContent = postcode;
         document.getElementById('preview-country').textContent = country;
         document.getElementById('preview-state').textContent = stateText;
         document.getElementById('preview-footer').textContent = footer;
         document.getElementById('preview-phone').textContent = phone;
         document.getElementById('preview-salesman').textContent = salesPerson;
         document.getElementById('preview-email').textContent = email;
         document.getElementById('preview-ssm').textContent = ssm;
         document.getElementById('preview-sst').textContent = sst;
    }

    // Add event listeners to form fields
    const fields = [
        'receipt_title',
        'receipt_address',
        'receipt_postcode',
        'receipt_city',
        'receipt_country',
        'receipt_state',
        'receipt_footer',
        'receipt_phone',
        'receipt_email',
        'receipt_ssm',
        'receipt_sst',
        'receipt_salesperson',
    ];

    fields.forEach(field => {
        const element = document.getElementById(field);
        if (element) {
            ['input', 'change'].forEach(event => {
                element.addEventListener(event, updatePreview);
            });
        }
    });

    // Logo preview function
    function previewLogo(input) {
        const preview = document.getElementById('logo-preview');
        const placeholder = document.getElementById('logo-placeholder');
        const previewContainer = document.getElementById('preview-logo-container');
        const previewLogo = document.getElementById('preview-logo');
        
        if (input.files && input.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Update form preview
                preview.src = e.target.result;
                preview.style.display = 'block';
                if (placeholder) placeholder.style.display = 'none';
                
                // Update receipt preview
                if (previewContainer) {
                    previewContainer.style.display = 'block';
                }
                if (previewLogo) {
                    previewLogo.src = e.target.result;
                }
            };
            reader.readAsDataURL(input.files[0]);
        }
    }

    // Remove logo function
    function removeLogo() {
        if (confirm('Are you sure you want to remove the logo?')) {
            // Create form data for logo removal
            const formData = new FormData();
            formData.append('_token', '{{ csrf_token() }}');
            formData.append('remove_logo', '1');
            
            fetch('{{ route('profile.receipt.remove-logo') }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.result) {
                    // Clear preview images with null checks
                    const logoPreview = document.getElementById('logo-preview');
                    const logoPlaceholder = document.getElementById('logo-placeholder');
                    const previewLogoContainer = document.getElementById('preview-logo-container');
                    const receiptLogoInput = document.getElementById('receipt_logo');
                    
                    if (logoPreview) {
                        logoPreview.style.display = 'none';
                        logoPreview.src = '#';
                    }
                    if (logoPlaceholder) {
                        logoPlaceholder.style.display = 'block';
                    }
                    if (previewLogoContainer) {
                        previewLogoContainer.style.display = 'none';
                    }
                    if (receiptLogoInput) {
                        receiptLogoInput.value = '';
                    }
                    
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Logo removed successfully.'
                    }).then(() => {
                        // Refresh the page to show updated state
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Failed to remove logo.'
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error', 
                    text: error.message || 'An unexpected error occurred.'
                });
            });
        }
    }

    // Handle form submission
    const form = document.getElementById('receipt_display_form');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            
            // Debug: Check if logo file is in FormData
            const logoFile = document.getElementById('receipt_logo').files[0];
            if (logoFile) {
                console.log('Logo file found:', logoFile.name, logoFile.size, logoFile.type);
                formData.set('receipt_logo', logoFile);
            } else {
                console.log('No logo file selected');
            }

            // Show loading state
            Swal.fire({
                title: 'Uploading...',
                text: 'Please wait while we save your receipt information.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                Swal.close();
                if (data.result) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: data.message || 'Receipt display information saved successfully.'
                    });
                    
                    // Refresh the page to show updated logo
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: data.message || 'Failed to save receipt display information.'
                    });
                }
            })
            .catch(error => {
                Swal.close();
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'An unexpected error occurred: ' + error.message
                });
            });
        });
    }

    // Handle form reset
    const resetButton = document.getElementById('reset-receipt-button');
    if (resetButton) {
        resetButton.addEventListener('click', function() {
            setTimeout(updatePreview, 0); // Update preview after form reset
        });
    }

    // Initial preview update
    updatePreview();

    // Initialize character counter for footer text
    const footerTextarea = document.getElementById('receipt_footer');
    const charCount = document.getElementById('charCount');
    if (footerTextarea && charCount) {
        charCount.textContent = footerTextarea.value.length;
        
        // Update counter on input
        footerTextarea.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
    }
</script>
@endpush
