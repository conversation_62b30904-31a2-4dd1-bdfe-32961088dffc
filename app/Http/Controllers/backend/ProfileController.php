<?php

namespace App\Http\Controllers\backend;

use App\Models\User;
use App\Models\Company;
use App\Models\Product;
use App\Models\Receipt;
use App\Models\Category;
use App\Models\Customer;
use App\Models\Employee;
use App\Models\Collection;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use App\Models\ProductsCategory;
use App\Models\CollectionProduct;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Support\Facades\Validator;
use App\Rules\NoEmojis;

class ProfileController extends Controller
{
    public function index(Request $request)
    {
        try {
            $categories = Category::whereNull('parent_id')->orderBy('name', 'ASC')->get();
            $user = $request->user();
            $receipt = $user->companyReceipts ?? null;
            
            // $detail = Cache::remember('user_detail_' . $user->id, 60*60*24, function() use($user){
            //     return $user->userDetails;
            // });

            $detail = $user->userDetails;
            $company = Cache::remember('user_company_' . $user->id, 60*60*24, function() use($user){
                return $user->companies;
            });
            
            return view('backend.profile', [
                'categories' => $categories,
                'user' => $user,
                'receipt' => $receipt,
                'detail' => $detail,
                'company' => $company
            ]);
        } catch (\Exception $e) {
            Log::error('Profile page failed to load ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
      
    }

    public function mmIndex()
    {
        return view('backend.master-merchant.mmprofile');
    }

    public function updatePassword(Request $request)
    {
        // Validation
        $validator = Validator::make($request->all(), [
            'plats_email' => 'email',
            'current_password' => 'required',
            'new_password' => [
                'required',
                'min:6',
                'confirmed', // Requires newPassword field
                'different:current_password', // New password must be different from current
                // Optional: Add more password complexity rules
                // 'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/'
            ]
        ], [
            // 'new_password.regex' => 'Password must contain at least one uppercase, one lowercase, one number, and one special character'
        ]);

        // Check validation
        if ($validator->fails()) {
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first(),
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();

        if (isset($request->plats_email)) {
            if (strtolower($request->plats_email) !== strtolower($user->email)) {
                return response()->json([
                    'result' => false,
                    'message' => 'The email address you entered does not match the one associated with your account. Please double-check the email or contact support if you need assistance.'
                ]);
            }
        }

        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'result' => false,
                'message' => 'Current password is incorrect'
            ], 422);
        }

        if (isBizappUserCheck()){
            try {
                $countryCodes = [
                    'MALAYSIA' => 'MY',
                    'INDONESIA' => 'ID',
                    'BRUNEI' => 'BN',
                    'SINGAPORE' => 'SG',
                ];
                
                $countryName = optional($user->employee)->company?->com_country;
                $countryCode = $countryCodes[$countryName] ?? null;

                $urlLatest = "https://corrad.visionice.net/bizapp/apigenerator_VERSIPOS.php?api_name=TRACK_UPDATE_PROFILE_BIZAPPOS";
                $urlPos = config('bizappos.bizappos_api_url');
    
                $getCurrentProfileInfo = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PROFILE&TX=', [
                    'pid' => $user->pid,
                    'TOKEN' => 'tok',
                    'DOMAIN' => $user->domain
                ])->throw()->json();
                if(isset($getCurrentProfileInfo[0]['STATUS']) && $getCurrentProfileInfo[0]['STATUS'] == '1'){
    
                    $updateToBizapp = Http::asForm()->post($urlLatest, [
                        'DOMAIN' => $user->domain,
                        'pid' => $user->pid,
                        'nama' => $user->userDetails->first_name . ' ' . $user->userDetails->last_name,
                        'nossm' => $request->ssm,
                        "jenissyarikat" => $getCurrentProfileInfo[0]['jenissyarikat'],
                        "bisnessektor" => $getCurrentProfileInfo[0]['bisnessektor'],
                        'namasyarikat' => $request->comname,
                        'nohp' => $user->userDetails->mobile,
                        'nooffice' => optional($user->employee)->company?->com_mobile 
                                            ?? $user->companies?->com_mobile 
                                            ?? 'N/A',
                        'alamat1' => $request->address,
                        'podkod' => $request->postcode,
                        'negeri' => $request->state,
                        'negara' => $countryCode,
                        'emel' => $user->email,
                        'penggunaid' => $user->username,
                        'katalaluan' => $request->new_password,
                    ])->throw()->json();
    
                } else {
                    Log::error('get profile in change-password does not return 1 ' . $getCurrentProfileInfo[0]);
                    return response()->json([
                        'status' => false,
                        'message' => 'Profile update failed, please contact admin for info'
                    ]);
                }
            } catch (\Exception $e){
                Log::error('Change password failed : ' . $e->getMessage());
            }
        }
       
        $user->update(['password' => Hash::make($request->new_password)]);
        $user->tokens()->delete();

        return response()->json([
            'result' => true,
            'message' => 'Password changed successfully, please check with your registered email for password-change notice.',
            'redirect' => route('login')
        ]);
    }

    public function updateProfile(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email:rfc,dns',
            'username' => 'required|unique:users,username,'.auth()->user()->id,
            'mobile' => 'required|numeric|min:10'
            // 'category' => 'required|exists:categories,id'
        ],[
            // 'category.exists' => 'Category not found',
            // 'category.required' => 'Category is required',
            'email.required' => 'Email is required',
            'email.email' => 'Email is invalid',
            'email.unique' => 'Email is already taken',
            'username.required' => 'Username is required',
            'username.unique' => 'Username is already taken',
            'mobile.required' => 'Mobile is required',
            'mobile.numeric' => 'Mobile must be numeric',
            'mobile.min' => 'Mobile must be at least 10 characters',
            'salesperson.required' => 'Sales Person name is required, this will appear on your receipt'
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $user = $request->user();
        $user->update([
            'email' => $request->email,
            'username' => $request->username,
        ]);
        $user->userDetails?->update([
            'mobile' => $request->mobile,
            'first_name' => $request->salesperson,
        ]);
        $user->companies->update([
            'category_id' => $request->category
        ]);

        Cache::forget('user_detail_' . $user->id);
        Cache::forget('user_company_' . $user->id);
        Cache::forget('profile_' . $user->id);

        return response()->json([
            'result' => true,
            'message' => 'Your profile has been updated'
        ]);
    }

    public function updateBusiness(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'comname' => 'required|string',
            'ssm' => 'required|string',
            'address' => 'required|string',
            'postcode' => 'required|numeric|min:5',
            'city' => 'required|string',
            'state' => 'required|string',
            'country' => 'required|string',
            'sst_percentage' => 'numberic|min:1|max:100',

            // 'category' => 'required|exists:categories,id'
        ],[
            // 'category.exists' => 'Category not found',
            // 'category.required' => 'Category is required',
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }
        $user = auth()->user();
        Log::info('updating profile for ' . $user->username . ' : ' . json_encode($request->all()));
        
        $user->companies?->update([
            'com_name' => $request->comname,
            'com_address' => $request->address,
            'com_postcode' => $request->postcode,
            'com_city' => $request->city,
            'com_state' => $request->state,
            'com_country' => $request->country,
            'com_registration_no' => $request->ssm, 
            'com_sst_number' => $request->sst_num,
            'com_sst_value' => $request->customCheck ? null : $request->sst_percent
        ]);
        $user->userDetails?->update([
            'address' => $request->address,
            'postcode' => $request->postcode,
            'city' => $request->city,
            'state' => $request->state,
            'country' => $request->country,
        ]);
        // $user->companies->update([
        //     'category_id' => $request->category
        // ]);

        Cache::forget('user_detail_' . $user->id);
        Cache::forget('user_company_' . $user->id);
        Cache::forget('profile_' . $user->id);

        $countryCodes = [
            'MALAYSIA' => 'MY',
            'INDONESIA' => 'ID',
            'BRUNEI' => 'BN',
            'SINGAPORE' => 'SG',
        ];
        
        $countryName = $request->country;
        $countryCode = $countryCodes[$countryName] ?? null;

        if($user->isBizappUser === 'Y' && app()->environment('production')){

            try {            
            $urlPos = config('bizappos.bizappos_api_url');
            $urlLatest = "https://corrad.visionice.net/bizapp/apigenerator_VERSIPOS.php?api_name=TRACK_UPDATE_PROFILE_BIZAPPOS";
            // get current profile from bizapp first
            $getCurrentProfileInfo = Http::asForm()->post($urlPos . 'api_name=TRACK_GET_PROFILE&TX=', [
                'pid' => $user->pid,
                'TOKEN' => 'tok',
                'DOMAIN' => $user->domain
            ])->throw()->json();
            if(isset($getCurrentProfileInfo[0]['STATUS']) && $getCurrentProfileInfo[0]['STATUS'] == '1'){
               
            $getAllCollectionsAPI = Http::asForm()->post($urlLatest, [
                'DOMAIN' => $user->domain,
                'pid' => $user->pid,
                'nama' => $user->userDetails->first_name . ' ' . $user->userDetails->last_name,
                'nossm' => $request->ssm,
                "jenissyarikat" => $getCurrentProfileInfo[0]['jenissyarikat'],
                "bisnessektor" => $getCurrentProfileInfo[0]['bisnessektor'],
                'namasyarikat' => $request->comname,
                'nohp' => $user->userDetails->mobile,
                // 'nokp' => null,
                'nooffice' => optional($user->employee)->company?->com_mobile ?? $user->companies?->com_mobile ?? 'N/A',
                'alamat1' => $request->address,
                // 'alamat2' => '',
                // 'alamat3' => '',
                'podkod' => $request->postcode,
                'negeri' => $request->state,
                'negara' => $countryCode,
                'emel' => $user->email,
                'penggunaid' => $user->username,
                'katalaluan' => 'MYOLDPASSWORD_BIZAPP',
            ])->throw()->json();
            Log::info('sending profile update to bizapp ' . json_encode($getAllCollectionsAPI[0]));

            } else {
                Log::error('get profile does not return 1 ' . $getCurrentProfileInfo[0]);
                return response()->json([
                    'status' => false,
                    'message' => 'Profile update failed, please contact admin for info'
                ]);
            }
        } catch (\Exception $e) {
            Log::error('getting profile info from bizapp failed ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }

    
        }
        // delete token
        // $request->user()->tokens()->delete();
        return response()->json([
            'result' => true,
            'message' => 'Your profile has been updated'
        ]);
    }

    public function updateProfilePic(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ],[
            'avatar.required' => 'Image is required',
            'avatar.image' => 'Image must be an image',
            'avatar.mimes' => 'Image must be jpeg, png, jpg',
            'avatar.max' => 'Image must be less than 2MB'
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $user = $request->user();
        // $detail = Cache::remember('user_detail_' . $user->id, 60*60*24, function() use($user){
        //     return $user->userDetails;
        // });
        $detail = $user->userDetails;
        // if user does not have a link with Bizapp

        if($user->isBizappUser === 'N'){

            $path = public_path().'/uploads/images/useravatar/';
            $filename = $user->id.'.'.$request->avatar->getClientOriginalExtension();
    
            if($detail->avatar != '' || $detail->avatar != null){
                $file_old = $path.$detail->avatar;
                unlink($file_old);
            }
    
            $request->avatar->move($path, $filename);
            $detail->update([
                'avatar' => $filename
            ]);
        } else {
            $urlX = config('bizappos.bizappx_api_url');
            try {
                // Retrieve the file from the request
                $file = $request->file('avatar');
                $filename = uniqid() . '_' . $file->getClientOriginalName(); // Unique file name
                $path = public_path().'/uploads/images/useravatar/';
    
                // Retrieve additional fields
                $domain = $user->domain;
                $pid = $user->pid;
                $token = 'tok';
    
                $updateProfileImage = Http::attach(
                    'file', // Key name for the file in the request
                    file_get_contents($file->getRealPath()), // File contents
                    $filename // Filename
                )->asMultipart() // Ensure multipart form data
                ->post($urlX . 'api_name=TRACK_SAVE_ATTACHMENT_PROFILE_PHOTO&TX=', [
                    'pid' => $user->pid,
                    'TOKEN' => 'token',
                    'DOMAIN' => $user->domain
                ])->throw()->json();
                // Log or process additional fields if needed
                Log::info("Profile photo uploaded for PID: $pid, DOMAIN: $domain, TOKEN: $token");
    
                // get new image path from Bizapp
                $urlPos = config('bizappos.bizappos_api_url');
                $bizappLogin = Http::asForm()->post($urlPos . 'api_name=TRACK_LOGIN', [
                    'DOMAIN' => $user->domain,
                    'username' => $user->username,
                    'password' => config('bizappos.bizapp_mpw'),
                    'platform' => 'POS'
                ])->throw()->json();

                if ($bizappLogin[0]['STATUS'] != "0"){
                    $userLocal = User::where('pid',$bizappLogin[0]['pid'])->first();
                    $userDetails = $userLocal->userDetails;

                    $userDetails->avatar = $bizappLogin[0]['attachmentphoto'];
                    $userDetails->save();
                }
                // Return success response
                return response()->json([
                    'status' => 'success',
                    'result' => true,
                    'message' => 'Profile photo uploaded successfully',
                ], 200);
            } catch (\Exception $e) {
                // Handle error
                Log::error("Error uploading profile photo: " . $e->getMessage());
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to upload profile photo',
                ], 500);
            }
        }
        
        Cache::forget('user_detail_' . $user->id);
        Cache::forget('user_company_' . $user->id);
        Cache::forget('user_' . $user->id);
        Cache::forget('profile_' . $user->id);

        return response()->json([
            'result' => true,
            'message' => 'Your avatar has been updated'
        ]);
    }

    public function updateReceiptDisplay(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'receipt_title' => ['required', 'string', new NoEmojis()],
            'receipt_address' => ['required', 'string', new NoEmojis()],
            'receipt_state' => 'required|string',
            'receipt_postcode' => ['required', 'string', new NoEmojis()],
            'receipt_city' => ['required', 'string', new NoEmojis()],
            'receipt_country' => 'required|string',
            'receipt_phone' => ['required', 'string', new NoEmojis()],
            'receipt_email' => ['required', 'string', new NoEmojis()],
            'receipt_salesperson' => ['required', 'string', new NoEmojis()],
            'receipt_ssm' => ['nullable', 'string', new NoEmojis()],
            'receipt_sst' => ['nullable', 'string', new NoEmojis()],
            'receipt_footer' => ['nullable', 'string', new NoEmojis()],
            'receipt_logo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048']
        ]);

        if($validator->fails()){
            return response()->json([
                'result' => false,
                'message' => $validator->errors()->first()
            ]);
        }
        $user = $request->user();
        $company = $user->companies;

        if (!$company) {
            return response()->json([
                'result' => false,
                'message' => 'Company not found for this user.'
            ], 404);
        }

        $receipt = Receipt::where('company_id', $company->id)->first();

        $existingSst = $receipt?->sst ?? '';
        $parts = explode(',', $existingSst);
        $existingSecondPart = isset($parts[1]) ? trim($parts[1]) : '0';
        $newFirstPart = trim($request->receipt_sst);
        $sstToSave = $newFirstPart . ',' . $existingSecondPart;

        $data = [
            'company_id' => $company->id,
            'user_id' => $user->id,
            'name' => $request->receipt_salesperson,
            'ssm' => $request->receipt_ssm,
            'sst' => $sstToSave,
            'address' => $request->receipt_address,
            'postcode' => $request->receipt_postcode,
            'city' => $request->receipt_city,
            'state' => $request->receipt_state,
            'country' => $request->receipt_country,
            'email' => $request->receipt_email,
            'phone' => $request->receipt_phone,
            'tnc' => $request->receipt_footer,
        ];

        // Handle logo upload to S3
        if ($request->hasFile('receipt_logo')) {
            Log::info('Processing logo upload for company ID: ' . $company->id);
            
            // Delete old logo if exists
            if ($receipt && $receipt->logo) {
                Storage::disk('s3')->delete($receipt->logo);
                Log::info('Deleted old logo: ' . $receipt->logo);
            }
            
            $companyId = $company->id;
            $logoPath = $request->file('receipt_logo')->store("profile/logo/{$companyId}", 's3');
            $data['logo'] = $logoPath;
            
            // Log::info('Logo uploaded successfully', [
            //     'path' => $logoPath,
            //     'company_id' => $companyId
            // ]);
        } else {
            // Log::info('No logo file received in request');
        }

        // Log::info('Data to be saved:', $data);

        if ($receipt) {
            $receipt->update($data);
            Log::info('Receipt updated with ID: ' . $receipt->id);
        } else {
            $newReceipt = Receipt::create($data);
            Log::info('New receipt created with ID: ' . $newReceipt->id);
        }

        return response()->json([
            'result' => true,
            'message' => 'Receipt display information saved successfully. Please refresh the Bizappos App to start implement the new changes.'
        ]);
    }

    public function removeReceiptLogo(Request $request)
    {
        $user = $request->user();
        $company = $user->companies;

        if (!$company) {
            return response()->json([
                'result' => false,
                'message' => 'Company not found for this user.'
            ], 404);
        }

        $receipt = Receipt::where('company_id', $company->id)->first();

        if (!$receipt || !$receipt->logo) {
            return response()->json([
                'result' => false,
                'message' => 'No logo found to remove.'
            ], 404);
        }

        // Delete logo from S3
        Storage::disk('s3')->delete($receipt->logo);
        
        // Update receipt record
        $receipt->update(['logo' => null]);

        return response()->json([
            'result' => true,
            'message' => 'Logo removed successfully.'
        ]);
    }
}
